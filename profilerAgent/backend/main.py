"""
FastAPI主应用
Agent的HTTP包装器
"""

import logging
import sys
import os
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse

# 加载环境变量
try:
    from dotenv import load_dotenv
    # 加载项目根目录下的.env文件
    project_root = os.path.dirname(os.path.dirname(__file__))
    env_path = os.path.join(project_root, '.env')
    load_dotenv(env_path)
    print(f"Loaded .env from: {env_path}")
except ImportError:
    print("python-dotenv not installed, using system environment variables")
    project_root = os.path.dirname(os.path.dirname(__file__))

# 添加项目根路径到Python路径
sys.path.insert(0, project_root)

# 导入Agent相关 - 使用新的工厂模式
from agent import (
    initialize_agent_system, get_agent_interface, get_system_status,
    cleanup_agent_system, AgentInterface
)

# 导入Backend组件
from database.connection import get_db_engine, get_redis_client, close_connections
from middleware.cors import add_cors_middleware
from api.health import router as health_router
from api.auth import router as auth_router
from api.voice import router as voice_router
from api.linkedin import router as linkedin_router
from api.profile import router as profile_router
from api.matches import router as matches_router
from api.users import router as users_router
from api.test_voice import router as test_voice_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局Agent实例
agent_instance: AgentInterface = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("Starting Dating App Backend...")
    
    try:
        # 初始化Agent
        await initialize_agent()
        logger.info("Agent initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize application: {e}")
        raise
    finally:
        # 关闭时清理
        logger.info("Shutting down Dating App Backend...")
        await cleanup()

async def initialize_agent():
    """初始化Agent实例 - 使用新的工厂模式"""
    global agent_instance

    try:
        # 获取Redis连接（Agent工厂会处理数据库连接）
        redis_client = get_redis_client()
        logger.info("Redis connection established")

        # 使用Agent工厂初始化整个系统
        await initialize_agent_system(redis_client)

        # 获取Agent接口
        agent_instance = get_agent_interface()

        # 验证Agent健康状态
        health_status = agent_instance.get_health_status()
        if not health_status.success:
            raise Exception(f"Agent health check failed: {health_status.error}")

        # 获取系统状态
        system_status = get_system_status()
        logger.info(f"Agent system initialized with {len(system_status.get('components', {}))} components")

    except Exception as e:
        logger.error(f"Failed to initialize Agent: {e}")
        raise

async def cleanup():
    """清理资源"""
    global agent_instance

    try:
        # 使用Agent工厂清理系统
        await cleanup_agent_system()
        agent_instance = None

        # 关闭backend的数据库连接
        await close_connections()

        logger.info("Cleanup completed")

    except Exception as e:
        logger.error(f"Error during cleanup: {e}")

# 创建FastAPI应用
app = FastAPI(
    title="Dating App API",
    description="AI-powered dating app backend with voice profiling",
    version="1.0.0-mvp",
    lifespan=lifespan
)

# 添加CORS中间件
add_cors_middleware(app)

# 添加路由
app.include_router(health_router)
app.include_router(auth_router)
app.include_router(voice_router)
app.include_router(linkedin_router)
app.include_router(profile_router)
app.include_router(matches_router)
app.include_router(users_router)
app.include_router(test_voice_router, prefix="/api/v1")

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理器"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Dating App API",
        "version": "1.0.0-mvp",
        "status": "running",
        "docs": "/docs"
    }

# 获取Agent实例的辅助函数
def get_agent() -> AgentInterface:
    """获取Agent实例"""
    if agent_instance is None:
        raise HTTPException(
            status_code=503,
            detail="Agent service not available"
        )
    return agent_instance

if __name__ == "__main__":
    import uvicorn
    
    # 开发环境运行
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
